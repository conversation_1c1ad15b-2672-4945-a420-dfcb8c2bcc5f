{"run": {"command": "nx build soundscape-web", "startTime": "2025-06-01T03:35:16.410Z", "endTime": "2025-06-01T03:35:18.166Z", "inner": false}, "tasks": [{"taskId": "soundscape-monorepo-redux:build", "target": "build", "projectName": "soundscape-monorepo-redux", "hash": "10881029152335636569", "startTime": "2025-06-01T03:35:16.418Z", "endTime": "2025-06-01T03:35:16.421Z", "params": "", "cacheStatus": "local-cache-hit", "status": 0}, {"taskId": "soundscape-web:build", "target": "build", "projectName": "soundscape-web", "hash": "13704936411609769365", "startTime": "2025-06-01T03:35:16.423Z", "endTime": "2025-06-01T03:35:18.158Z", "params": "", "cacheStatus": "cache-miss", "status": 0}]}