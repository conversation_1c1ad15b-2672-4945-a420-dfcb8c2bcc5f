'use strict';

const DataTypes = require('sequelize');

module.exports = (sequelize) => {
  return sequelize.define('spot_schedule',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },

      name: DataTypes.STRING,
      description: DataTypes.STRING,
      number_of_spots: DataTypes.INTEGER,
      is_active: DataTypes.BOOLEAN,
      created_date: DataTypes.DATE,
      update_date: DataTypes.DATE,
    }, {
    tableName: 'spot_schedules'
  });
};