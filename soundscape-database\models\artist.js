'use strict';

const DataTypes = require('sequelize');

module.exports = (sequelize) => {
  const Artist = sequelize.define('artist',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },

      url: DataTypes.STRING,
      name: DataTypes.STRING,
      description: DataTypes.STRING,
      image: DataTypes.STRING,
      created_date: DataTypes.DATE,
      updated_date: DataTypes.DATE
    }, {
    tableName: 'artists'
  });

  Artist.associate = (models) => {
    Artist.hasMany(models.track_artist, { foreignKey: 'artist_id', as: 'trackArtists' });
    Artist.belongsToMany(models.album, {
      through: models.album_artist,
      foreignKey: 'artist_id',
      as: 'albums'
    });
    Artist.hasMany(models.album_artist, { foreignKey: 'artist_id', as: 'albumArtists' });
  };

  return Artist;
};
