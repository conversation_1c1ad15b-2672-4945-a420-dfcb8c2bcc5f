import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { fetchGenresAsync, fetchGenreChannels } from '../thunks/genreThunks';

// Define interfaces
export interface Genre {
  id: number;
  name: string;
  color: string;
  icon: string;
  [key: string]: any;
}

export interface Channel {
  id: string;
  name: string;
  description?: string;
  imageUrl?: string;
  tracks?: any[];
  [key: string]: any;
}

export interface GenreState {
  items: Genre[];
  loading: boolean;
  error: string | null;
  lastFetched: number | null;
  selectedGenre: number | null;
  channelsById: { [genreId: string]: Channel[] };
  channelsLoading: boolean;
  channelsError: string | null;
}

const initialState: GenreState = {
  items: [],
  loading: false,
  error: null,
  lastFetched: null,
  selectedGenre: null,
  channelsById: {},
  channelsLoading: false,
  channelsError: null
};

const genreSlice = createSlice({
  name: 'genres',
  initialState,
  reducers: {
    setSelectedGenre: (state, action: PayloadAction<number | null>) => {
      state.selectedGenre = action.payload;
    }
  },
  extraReducers: (builder) => {
    // Handle fetchGenresAsync
    builder
      .addCase(fetchGenresAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchGenresAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload;
        state.lastFetched = Date.now();
      })
      .addCase(fetchGenresAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string || 'Failed to fetch genres';
      });

    // Handle fetchGenreChannels
    builder
      .addCase(fetchGenreChannels.pending, (state) => {
        state.channelsLoading = true;
        state.channelsError = null;
      })
      .addCase(fetchGenreChannels.fulfilled, (state, action) => {
        state.channelsLoading = false;
        // Extract genreId and channels from the response
        const { genreId } = action.payload;
        const { channels } = action.payload;

        // Store the channels in the state
        state.channelsById[genreId] = channels;
      })
      .addCase(fetchGenreChannels.rejected, (state, action) => {
        state.channelsLoading = false;
        state.channelsError = action.payload as string || 'Failed to fetch genre channels';
      });
  }
});

export const { setSelectedGenre } = genreSlice.actions;
export default genreSlice.reducer;