{"9290992100371605347": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "6825641854078573337": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "13735182541885711725": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "2778344090687930262": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "18385819752450368819": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "14236029401124022845": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "11066062079983609308": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "17726402084442263421": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "14313124603425878935": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "15314102790164990225": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "14931730697296714176": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "5258821992604870910": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "10353687474268791686": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "18121032604708308887": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "3442087723254771586": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "4270386576777550559": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "3975104097207296872": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "12240661282868222577": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "8969717506729086298": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "1460957987782792758": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "15372505080572382036": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "4008978577282057210": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "18310303467762192286": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "13307049466642045132": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "17133811284858607013": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "2158998674908073588": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "16040077868379706720": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "2414263432049408463": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "16676593822368271856": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "13365491859687034883": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "5336266137041764913": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "2467105242473566495": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "7434776423316371175": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "7762871728202817688": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "16523079607950781785": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "8458313213220488344": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "11956292298372091959": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "730926885031852926": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "13941438882340307843": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "12036249500616430903": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "12646833959748702240": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "14905267428041047276": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "11276610856686693170": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "17106623739609753008": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "12180183171821215536": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "4153405302030976115": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "9463220156700539010": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "15988782207947098009": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "11793931696307791730": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "394245353225888889": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "16918759620477959669": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "12563648920404864046": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "11965092358204529903": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "17287519829879337791": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "14144219029816221182": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "15038087598603107546": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "6214304032475749722": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "12342008223375068921": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "11448338916829764330": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "14743245692569345120": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "5991460363593719476": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "1376963040410879720": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "3273915966841986006": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "17061449828405689210": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "5668440213040803562": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "18006817615388021828": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "18348913668818090183": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "13077814733853468489": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "10187392287353320800": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "6664693719172032885": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "17899362522931807011": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "13206788904598750426": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "3257249714684683918": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "8601268346974486009": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "13190333499150795987": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "7535466430768667373": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "18255084911317456814": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "8720785789829456545": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "16965528085765684667": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "5878703321291250898": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "3832440190497379922": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "11201535423573911840": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "17710593087481643507": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "42776218424421715": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "6314708214998940615": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "13485640874026987445": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "67849766063632912": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "5719073563402135313": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "8366427262061104249": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "2699272229966008818": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "13714079211142845080": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "7542490738383670446": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "14347696834968303089": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "6697257493057280989": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "12444873807353935632": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "1496575535662172830": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "7740638042378493104": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "13649232189852251497": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "11828035428700725127": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "6725454106637085500": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "724259763262959958": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "7061376592504824819": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "8449514044504777916": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "16093019499598672095": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "17305002541631398079": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "12011806537927449260": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "8355533533716850518": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "3840322731365098657": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "4953582566068907487": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "12625192415556081266": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "3958423275391253141": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "9846549273110599658": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "12915013464885135906": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "16956214817542123306": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "4270463826292728320": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "784243652534659792": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "10309737366728231828": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "11734026034131026218": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "7214421278133999078": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "16454271136044313137": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "10510494822500629438": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "12807084816767856915": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "8800870658469095647": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "6135201126938123080": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "1473599169131291038": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "11164402208646406180": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}, "14166286317532312921": {"targets": {"test": {"command": "jest", "options": {"cwd": "soundscape-monorepo-redux", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\"}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}\\coverage\\soundscape-monorepo-redux"]}}}}