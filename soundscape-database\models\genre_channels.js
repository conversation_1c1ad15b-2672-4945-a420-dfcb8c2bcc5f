'use strict';

const DataTypes = require('sequelize');

module.exports = (sequelize) => {
  const GenreChannel = sequelize.define('genre_channel',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      genre_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'genres',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      channel_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'channels',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      order: DataTypes.INTEGER,
      is_public: DataTypes.BOOLEAN,
      is_active: DataTypes.BOOLEAN,
      created_date: DataTypes.DATE,
      updated_date: DataTypes.DATE
    },
    {
      tableName: 'genre_channels',
      timestamps: false
    }
  );

  // Define associations
  GenreChannel.associate = (models) => {
    //console.log("models genrechannels assosiate:",models);
    GenreChannel.belongsTo(models.genre, { foreignKey: 'genre_id', as: 'genre' });
    GenreChannel.belongsTo(models.channel, { foreignKey: 'channel_id', as: 'channel' });
  };

  return GenreChannel;
};
