import React, { useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  playTrack,
  pauseTrack,
  nextTrack,
  previousTrack,
  setProgress,
  setDuration,
  startChannelStreaming,
  skipToNextTrack,
  pauseSong,
  resumeSong,
  RootState,
  AppDispatch
} from '@soundscape-monorepo/soundscape-monorepo-redux';
import './AudioPlayer.css';

// Interface for track data
interface TrackData {
  title?: string;
  albumImage?: string;
  albumArt?: string;
  albumTitle?: string;
  [key: string]: any;
}

const AudioPlayer: React.FC = () => {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const dispatch = useDispatch<AppDispatch>();

  // Get player state (for channel streaming)
  const {
    isPlaying: playerIsPlaying,
    currentTrack,
    playlist,
    channelId,
    streamUrl,
    progress: playerProgress,
    duration: playerDuration
  } = useSelector((state: RootState) => state.player);

  // Get musicPlayer state (for direct song URLs)
  const {
    currentSong,
    isPlaying: musicPlayerIsPlaying,
    progress: musicPlayerProgress
  } = useSelector((state: RootState) => state.musicPlayer);

  // Determine which player is active and combine states
  const isChannelMode = playlist.length > 0 && currentTrack >= 0;
  const isDirectMode = !!currentSong;
  const isPlaying = isChannelMode ? playerIsPlaying : musicPlayerIsPlaying;
  // We'll use the original variables directly in the UI
  const currentSongData: TrackData = isChannelMode ? (playlist[currentTrack] || {}) : (currentSong || {});

  // Helper function to get the main artist name
  const getMainArtist = (track: any) => {
    if (!track) return '';

    if (isChannelMode) {
      if (!track.artists) return '';
      const mainArtist = track.artists.find((a: any) => a.role === 'Primary') || track.artists[0];
      return mainArtist ? mainArtist.name : '';
    } else {
      return track.artist || '';
    }
  };

  // Consolidated audio source management for channel mode
  useEffect(() => {
    if (isChannelMode && audioRef.current && playlist.length > 0) {
      const currentTrackData = playlist[currentTrack];
      if (currentTrackData && currentTrackData.url) {
        const newSrc = currentTrackData.url;

        // Only update source if it's different to avoid unnecessary reloads
        if (audioRef.current.src !== newSrc) {
          console.log(`Setting audio source for track ${currentTrack}: ${currentTrackData.title}`);
          console.log('Audio URL:', newSrc);

          audioRef.current.src = newSrc;
          audioRef.current.currentTime = 0;
        }
      }
    }
  }, [isChannelMode, currentTrack, playlist]);

  // Handle play/pause state for channel mode
  useEffect(() => {
    if (isChannelMode && audioRef.current) {
      if (playerIsPlaying) {
        // Add a small delay to ensure the source is loaded
        const playAudio = async () => {
          try {
            await audioRef.current?.play();
          } catch (error) {
            console.error('Error playing audio:', error);
            dispatch(pauseTrack());
          }
        };

        // Small delay to avoid conflicts with source changes
        setTimeout(playAudio, 100);
      } else {
        audioRef.current.pause();
      }
    }
  }, [isChannelMode, playerIsPlaying, dispatch]);

  // Handle direct playing in musicPlayer
  useEffect(() => {
    if (isDirectMode && currentSong && audioRef.current) {
      audioRef.current.src = currentSong.url;

      if (musicPlayerIsPlaying) {
        audioRef.current.play().catch(error => {
          console.error('Error playing audio:', error);
          dispatch(pauseSong());
        });
      } else {
        audioRef.current.pause();
      }
    }
  }, [isDirectMode, currentSong, musicPlayerIsPlaying, dispatch]);

  // Note: Play/pause logic is now handled in the separate channel and direct mode effects above

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      if (isChannelMode) {
        dispatch(setProgress(audioRef.current.currentTime));
      } else {
        const currentProgress = (audioRef.current.currentTime / audioRef.current.duration) * 100;
        dispatch({ type: 'UPDATE_PROGRESS', payload: currentProgress });
      }
    }
  };

  const handleLoadedMetadata = () => {
    if (audioRef.current && isChannelMode) {
      dispatch(setDuration(audioRef.current.duration));
    }
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const progressBar = e.currentTarget;
    const clickPosition = (e.clientX - progressBar.getBoundingClientRect().left) / progressBar.offsetWidth;

    if (audioRef.current) {
      if (isChannelMode) {
        audioRef.current.currentTime = clickPosition * playerDuration;
      } else {
        audioRef.current.currentTime = clickPosition * audioRef.current.duration;
      }
    }
  };

  const handleEnded = () => {
    if (isChannelMode) {
      // First update the UI
      dispatch(nextTrack());

      // Then tell the API to skip to the next track
      if (channelId) {
        dispatch(skipToNextTrack({ channelId, currentTrack }))
          .unwrap()
          .then((nextTrackIndex: number) => {
            console.log(`Skipped to track ${nextTrackIndex}`);
          })
          .catch((error: Error) => {
            console.error('Failed to skip track:', error);
          });
      }
    } else {
      dispatch(pauseSong());
    }
  };

  const handlePlayPause = () => {
    if (isChannelMode) {
      dispatch(isPlaying ? pauseTrack() : playTrack());
    } else {
      dispatch(isPlaying ? pauseSong() : resumeSong());
    }
  };

  // Don't render if there's no content to play
  if (!isChannelMode && !isDirectMode) return null;

  // Safely access properties
  const albumImageSrc = isChannelMode
    ? (currentSongData as any)?.albumImage
    : (currentSongData as any)?.albumArt;

  const trackTitle = (currentSongData as any)?.title || 'Unknown Track';
  const albumTitle = (currentSongData as any)?.albumTitle;

  return (
    <div className="music-player">
      <audio
        ref={audioRef}
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
        onEnded={handleEnded}
      />

      <div className="music-player-content">
        <div className="player-left">
          <div className="album-art">
            <img
              src={albumImageSrc || '/default-album-art.jpg'}
              alt="Album Art"
            />
          </div>
          <div className="song-info">
            <h3>{trackTitle}</h3>
            <p>{getMainArtist(currentSongData)}</p>
            {isChannelMode && albumTitle && <p>{albumTitle}</p>}
          </div>
        </div>

        <div className="player-center">
          <div className="player-controls">
            <button
              className="control-button"
              onClick={() => isChannelMode && dispatch(previousTrack())}
              disabled={!isChannelMode}
            >
              <svg viewBox="0 0 24 24" width="24" height="24">
                <path d="M6 6h2v12H6zm3.5 6l8.5 6V6z" fill={isChannelMode ? "currentColor" : "#888"} />
              </svg>
            </button>

            <button
              className="control-button play-pause"
              onClick={handlePlayPause}
            >
              {isPlaying ? (
                <svg viewBox="0 0 24 24" width="32" height="32">
                  <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" fill="currentColor" />
                </svg>
              ) : (
                <svg viewBox="0 0 24 24" width="32" height="32">
                  <path d="M8 5v14l11-7z" fill="currentColor" />
                </svg>
              )}
            </button>

            <button
              className="control-button"
              onClick={() => {
                if (isChannelMode && channelId) {
                  // First update the UI
                  dispatch(nextTrack());

                  // Then tell the API to skip to the next track
                  dispatch(skipToNextTrack({ channelId, currentTrack }))
                    .unwrap()
                    .then((nextTrackIndex: number) => {
                      console.log(`Skipped to track ${nextTrackIndex}`);
                    })
                    .catch((error: Error) => {
                      console.error('Failed to skip track:', error);
                    });
                }
              }}
              disabled={!isChannelMode}
            >
              <svg viewBox="0 0 24 24" width="24" height="24">
                <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z" fill={isChannelMode ? "currentColor" : "#888"} />
              </svg>
            </button>
          </div>

          <div className="progress-container">
            <div
              className="progress-bar"
              onClick={handleProgressClick}
            >
              <div
                className="progress"
                style={{
                  width: isChannelMode
                    ? `${playerDuration ? (playerProgress / playerDuration) * 100 : 0}%`
                    : `${musicPlayerProgress}%`
                }}
                // Using the renamed variables
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AudioPlayer;