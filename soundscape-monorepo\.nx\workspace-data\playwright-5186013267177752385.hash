{"13669908512166054128": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\test-output\\src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\playwright-report\\src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\playwright-report\\src-example-spec-ts"}}, "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "15445352619750195470": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\test-output\\src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\playwright-report\\src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\playwright-report\\src-example-spec-ts"}}, "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "2914585366352490392": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\test-output\\src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\playwright-report\\src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\playwright-report\\src-example-spec-ts"}}, "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "15359506917296022589": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\test-output\\src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\playwright-report\\src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\playwright-report\\src-example-spec-ts"}}, "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "2050152852500207362": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\test-output\\src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\playwright-report\\src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\playwright-report\\src-example-spec-ts"}}, "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "13912310782279599448": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\test-output\\src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\playwright-report\\src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\playwright-report\\src-example-spec-ts"}}, "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "10260302339225672065": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\test-output\\src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\playwright-report\\src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\playwright-report\\src-example-spec-ts"}}, "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "1317698129460312719": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\test-output\\src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\playwright-report\\src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\playwright-report\\src-example-spec-ts"}}, "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "8055372505213248638": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\test-output\\src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\playwright-report\\src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "..\\..\\dist\\.playwright\\apps\\soundscape-web-e2e\\playwright-report\\src-example-spec-ts"}}, "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/soundscape-web-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "parallelism": false, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}}