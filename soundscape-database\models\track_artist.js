'use strict';

const DataTypes = require('sequelize');

module.exports = (sequelize) => {
  return sequelize.define('track_artist',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      // Foreign Key
      track_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'tracks',
          key: 'id'
        }
      },

      artist_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'artists',
          key: 'id'
        }
      },
      role: DataTypes.STRING,   // Primary, Featured, Collaborator etc...
    }, {
    tableName: 'track_artists',
    timestamps: false
  });
};