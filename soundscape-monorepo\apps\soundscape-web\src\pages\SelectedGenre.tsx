import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useSelector } from 'react-redux';
import './selectedgenre.css';

// Import from Redux package
import { 
  useAppDispatch, 
  fetchGenreChannels, 
  setSelectedGenre, 
  Genre, 
  Channel, 
  GenreState 
} from '@soundscape-monorepo/soundscape-monorepo-redux';

// Mock tracks data - in a real app, this would come from an API
const mockTracks = [
  { id: 1, title: 'Summer Breeze', artist: 'Melody Masters', duration: '3:45', popular: true },
  { id: 2, title: 'Midnight Drive', artist: 'The Journeymen', duration: '4:12', popular: true },
  { id: 3, title: 'Mountain High', artist: 'Altitude', duration: '3:22', popular: false },
  { id: 4, title: 'Ocean Waves', artist: 'Coastal Sound', duration: '5:07', popular: true },
  { id: 5, title: 'City Lights', artist: 'Urban Echo', duration: '3:56', popular: false },
  { id: 6, title: 'Desert Wind', artist: 'Sand Nomads', duration: '4:33', popular: false },
  { id: 7, title: 'Forest Rain', artist: 'Nature Sounds', duration: '6:21', popular: true },
  { id: 8, title: 'Starry Night', artist: 'Cosmic Band', duration: '4:45', popular: false }
];

// Mock artists for this genre
const mockArtists = [
  { id: 1, name: 'Melody Masters', followers: '2.3M', image: '👨‍🎤' },
  { id: 2, name: 'The Journeymen', followers: '1.8M', image: '👩‍🎤' },
  { id: 3, name: 'Altitude', followers: '950K', image: '🧑‍🎤' },
  { id: 4, name: 'Coastal Sound', followers: '1.5M', image: '👨‍🎤' },
  { id: 5, name: 'Urban Echo', followers: '750K', image: '👩‍🎤' }
];

const SelectedGenre = () => {
  const { genreId } = useParams<{ genreId: string }>();
  const dispatch = useAppDispatch();
  
  // Get genre data from Redux store
  const genreState = useSelector((state: any) => state.genres as GenreState);
  const allGenres = genreState?.items || [];
  const channels = genreState.channelsById[genreId || ''] || [];
  const channelsLoading = genreState.channelsLoading;
  const channelsError = genreState.channelsError;
  
  // Find the selected genre based on the ID from URL params
  const selectedGenre = allGenres.find(genre => genre.id === Number(genreId));
  
  // Setting up state for tracks
  const [popularTracks, setPopularTracks] = useState(mockTracks.filter(track => track.popular));
  const [allTracks, setAllTracks] = useState(mockTracks);
  
  // Fetch genre channels when component mounts
  useEffect(() => {
    if (genreId) {
      dispatch(setSelectedGenre(Number(genreId)));
      dispatch(fetchGenreChannels(genreId));
    }
  }, [genreId, dispatch]);
  
  // If we can't find the genre, show a not found message
  if (!selectedGenre) {
    return (
      <div className="selected-genre-page">
        <div className="genre-not-found">
          <h2>Genre Not Found</h2>
          <p>We couldn't find the genre you're looking for.</p>
          <Link to="/genres" className="back-button">Back to Genres</Link>
        </div>
      </div>
    );
  }

  return (
    <div className="selected-genre-page">
      <div 
        className="genre-header" 
        style={{ 
          backgroundColor: selectedGenre.color,
          backgroundImage: `linear-gradient(to bottom, ${selectedGenre.color}, ${selectedGenre.color}88)`
        }}
      >
        <Link to="/genres" className="back-button">
          ← Back to Genres
        </Link>
        <div className="genre-header-content">
          <div className="genre-icon">{selectedGenre.icon}</div>
          <h1>{selectedGenre.name}</h1>
          <p>Explore the best of {selectedGenre.name} music</p>
        </div>
      </div>

      <div className="genre-content">
        {/* Channels section from API */}
        <section className="genre-channels">
          <h2>Channels</h2>
          {channelsLoading && <p>Loading channels...</p>}
          {channelsError && <p className="error">Error loading channels: {channelsError}</p>}
          <div className="channels-grid">
            {channels.map((channel: Channel) => (
              <div className="channel-card" key={channel.id}>
                <div className="channel-image">
                  {channel.imageUrl ? (
                    <img src={channel.imageUrl} alt={channel.name} />
                  ) : (
                    <div className="placeholder-image">📻</div>
                  )}
                </div>
                <h3>{channel.name}</h3>
                <p>{channel.description || `${selectedGenre.name} channel`}</p>
              </div>
            ))}
            {!channelsLoading && channels.length === 0 && !channelsError && (
              <p>No channels found for this genre.</p>
            )}
          </div>
        </section>

        <section className="genre-popular-tracks">
          <h2>Popular Tracks</h2>
          <div className="tracks-list">
            {popularTracks.map(track => (
              <div className="track-item" key={track.id}>
                <div className="track-info">
                  <h3>{track.title}</h3>
                  <p>{track.artist}</p>
                </div>
                <div className="track-duration">{track.duration}</div>
              </div>
            ))}
          </div>
        </section>

        <section className="genre-featured-artists">
          <h2>Featured Artists</h2>
          <div className="artists-grid">
            {mockArtists.map(artist => (
              <div className="artist-card" key={artist.id}>
                <div className="artist-image">{artist.image}</div>
                <h3>{artist.name}</h3>
                <p>{artist.followers} followers</p>
              </div>
            ))}
          </div>
        </section>

        <section className="genre-all-tracks">
          <h2>All Tracks</h2>
          <div className="tracks-list">
            {allTracks.map(track => (
              <div className="track-item" key={track.id}>
                <div className="track-info">
                  <h3>{track.title}</h3>
                  <p>{track.artist}</p>
                </div>
                <div className="track-duration">{track.duration}</div>
              </div>
            ))}
          </div>
        </section>
      </div>
    </div>
  );
};

export default SelectedGenre;