const express = require('express');
const router = express.Router();
const { models } = require('../database');

router.get('/:channelId/tracks', async (req, res) => {
  try {
    const { channelId } = req.params;

    // Fetch channel info
    const channel = await models.channel.findOne({
      where: { id: channelId },
      include: [{
        model: models.channel_track,
        as: 'channelTracks',
        include: [{
          model: models.track,
          as: 'track',
          include: [
            {
              model: models.album,
              include: [{ model: models.artist }]
            },
            {
              model: models.track_artist,
              as: 'trackArtists',
              include: [{ model: models.artist }]
            }
          ]
        }]
      }]
    });

    if (!channel) {
      return res.status(404).json({ error: 'Channel not found' });
    }

    // Transform the data to match the frontend needs
    const tracks = channel.channelTracks.map(ct => ({
      id: ct.track.id,
      title: ct.track.title,
      url: ct.track.url,
      weight: ct.weight,
      albumId: ct.track.album_id,
      albumTitle: ct.track.album.title,
      albumImage: ct.track.album.image,
      artists: ct.track.trackArtists.map(ta => ({
        id: ta.artist.id,
        name: ta.artist.name,
        role: ta.role
      })),
      duration: ct.track.durationseconds
    }));

    // Return both channel info and tracks
    res.json({
      channelInfo: {
        id: channel.id,
        name: channel.name,
        description: channel.description,
        image: channel.image,
        isPublic: channel.is_public,
        isActive: channel.is_active
      },
      tracks
    });

  } catch (error) {
    console.error('Error fetching channel tracks:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;