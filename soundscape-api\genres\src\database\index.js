'use strict';

const path = require('path');
const { createConnection } = require('../../../shared/config/database');

const modelsPath = path.resolve(__dirname, './models');
const { connection, models } = createConnection(modelsPath);

// Initialize model associations
Object.keys(models).forEach(modelName => {
    if (models[modelName].associate) {
        console.log(`Setting up associations for model: ${modelName}`);
        models[modelName].associate(models);
    }
});

module.exports = {
    connection,
    models
};
