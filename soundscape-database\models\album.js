'use strict';

const DataTypes = require('sequelize');

module.exports = (sequelize) => {
  const Album = sequelize.define('album', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    url: DataTypes.STRING,
    image: DataTypes.STRING,
    title: {
      type: DataTypes.STRING,
      allowNull: false
    },
    description: DataTypes.STRING,
    release_date: {
      type: DataTypes.DATE,
      allowNull: false
    },
    year: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    total_tracks: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    album_type: {
      type: DataTypes.ENUM('album', 'single', 'compilation'),
      defaultValue: 'album'
    },
    hover_text: DataTypes.STRING,
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    created_date: DataTypes.DATE,
    updated_date: DataTypes.DATE,
    label_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'labels',
        key: 'id'
      }
    }
  }, {
    tableName: 'albums',
    timestamps: false
  });

  Album.associate = (models) => {
    Album.belongsTo(models.label, { foreignKey: 'label_id', as: 'label' });
    Album.hasMany(models.track, { foreignKey: 'album_id', as: 'tracks' });
    Album.belongsToMany(models.artist, {
      through: models.album_artist,
      foreignKey: 'album_id',
      as: 'artists'
    });
    Album.hasMany(models.album_artist, { foreignKey: 'album_id', as: 'albumArtists' });
  };

  return Album;
};
