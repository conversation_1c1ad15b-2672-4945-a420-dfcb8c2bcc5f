const db = require('../database');

class GenreService {
    constructor() {
    }

    async getGenres() {
        try {
            const activeGenres = await db.models.genre.findAll({
                where: {
                    is_active: true,
                    is_public: true
                },
                attributes: [
                    'id',
                    'url',
                    'name',
                    'image',
                    'order'
                ],
                order: [['order', 'ASC']], // Sort by defined order
            });
            return activeGenres;
        } catch (error) {
            console.error('Error fetching active genres:', error);
            throw error;
        }
    }

    async getGenreChannelsByGenreId(genreId) {
        try {
            const genre = await db.models.genre.findOne({
                where: { id: genreId },
                include: [{
                    model: db.models.genre_channel, // Fixed model name (singular)
                    as: 'genreChannels',
                    include: [{
                        model: db.models.channel, // Fixed model name (singular)
                        as: 'channel', // Specify the alias as defined in genre_channels.js
                        attributes: ['id', 'name', 'description', 'image']
                    }]
                }]
            });

            if (!genre) {
                const error = new Error('Genre not found');
                error.status = 404;
                error.error_code = 'not_found';
                error.error_description = 'Genre not found';
                throw error;
            }

            return {
                genre: {
                    id: genre.id,
                    name: genre.name,
                    description: genre.description,
                    image: genre.image,
                    path: genre.path
                },
                channels: genre.genreChannels && genre.genreChannels.length > 0 ?
                    genre.genreChannels.map(gc => {
                        // Check if channel exists before accessing its properties
                        if (!gc.channel) {
                            console.warn(`Channel not found for genre channel ID: ${gc.id}`);
                            return null;
                        }
                        return {
                            id: gc.channel.id,
                            name: gc.channel.name,
                            description: gc.channel.description,
                            image: gc.channel.image,
                            order: gc.order
                        };
                    }).filter(channel => channel !== null) : []
            };
        } catch (error) {
            if (error.error_code) {
                throw error;
            }
            console.error('Error fetching genre channels:', error);
            throw {
                status: 500,
                error_code: 'server_error',
                error_description: 'Error fetching genre channels'
            };
        }
    }
}

module.exports = GenreService;
